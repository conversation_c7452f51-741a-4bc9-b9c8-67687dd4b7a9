import { PsychologicalTestSummary } from "@/app/model/measure.model";
import { TestCategory } from "@/app/model/test.model";
import { useCallback, useEffect, useRef, useState } from "react";

// 分页接口
export interface Pagination {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

// 查询参数接口
export interface ToolsListParams {
  query?: string;
  category?: TestCategory;
  status?: string;
  dateRange?: [number, number];
}

// 请求参数接口
export interface ToolsListRequest {
  page: number;
  pageSize: number;
  params?: ToolsListParams;
  forceRefresh?: boolean;
}

// 错误类型
export interface ApiError {
  code: string;
  message: string;
  details?: unknown;
}

// 操作结果类型
export interface OperationResult<T = unknown> {
  success: boolean;
  data?: T;
  error?: ApiError;
}

// 工具状态枚举
export enum ToolStatus {
  ENABLED = "已启用",
  DISABLED = "未启用",
}

// 扩展的测量工具类型，包含状态字段
export interface AssessmentToolWithStatus extends PsychologicalTestSummary {
  status?: string;
  createdAt?: number;
  updatedAt?: number;
}

// 模拟API调用，后续替换为实际API
const mockFetchTools = async (
  params: ToolsListRequest
): Promise<{
  data: PsychologicalTestSummary[];
  pagination: Pagination;
}> => {
  console.log("mockFetchTools params", params);

  // 模拟数据
  const allTools = [
    {
      id: "sds-depression",
      title: "SDS抑郁自评量表",
      shortTitle: "抑郁自评",
      description: "评估抑郁症状严重程度的标准化测量工具",
      shortDescription: "快速筛查抑郁症状",
      category: TestCategory.EMOTION,
      howmany: 20,
      duration: 7,
      usersCompleted: 2400,
      icon: "/assets/icons/emotion-brain.svg",
      isFree: true,
      status: "已启用",
      createdAt: Date.now() - 30 * 24 * 60 * 60 * 1000, // 30天前
    },
    {
      id: "sas-anxiety",
      title: "SAS焦虑自评量表",
      shortTitle: "焦虑自评",
      description: "评估焦虑症状严重程度的标准化测量工具",
      shortDescription: "快速筛查焦虑症状",
      category: TestCategory.EMOTION,
      howmany: 20,
      duration: 5,
      usersCompleted: 2100,
      icon: "/assets/icons/anxiety-waves.svg",
      isFree: true,
      status: "已启用",
      createdAt: Date.now() - 25 * 24 * 60 * 60 * 1000, // 25天前
    },
    {
      id: "scl90",
      title: "SCL-90症状自评量表",
      shortTitle: "症状自评",
      description: "评估多种心理症状的综合自评量表",
      shortDescription: "全面筛查心理症状",
      category: TestCategory.GENERAL,
      howmany: 90,
      duration: 15,
      usersCompleted: 1800,
      icon: "/assets/icons/general-mind.svg",
      isFree: false,
      status: "已启用",
      createdAt: Date.now() - 20 * 24 * 60 * 60 * 1000, // 20天前
    },
    {
      id: "psqi-sleep",
      title: "匹兹堡睡眠质量指数(PSQI)",
      shortTitle: "睡眠质量",
      description: "评估睡眠质量的专业量表",
      shortDescription: "全面评估睡眠质量",
      category: TestCategory.SLEEP,
      howmany: 19,
      duration: 8,
      usersCompleted: 1500,
      icon: "/assets/icons/sleep-moon.svg",
      isFree: true,
      status: "已启用",
      createdAt: Date.now() - 15 * 24 * 60 * 60 * 1000, // 15天前
    },
    {
      id: "epq-personality",
      title: "艾森克人格问卷(EPQ)",
      shortTitle: "人格测验",
      description: "测量内外向、神经质等人格维度",
      shortDescription: "评估基本人格特质",
      category: TestCategory.PERSONALITY,
      howmany: 88,
      duration: 20,
      usersCompleted: 1200,
      icon: "/assets/icons/personality-brain.svg",
      isFree: false,
      status: "未启用",
      createdAt: Date.now() - 10 * 24 * 60 * 60 * 1000, // 10天前
    },
  ];

  // 应用筛选
  let filteredTools = [...allTools];

  if (params.params) {
    // 按查询词筛选
    if (params.params.query) {
      const query = params.params.query.toLowerCase();
      filteredTools = filteredTools.filter(
        (tool) =>
          tool.title.toLowerCase().includes(query) ||
          tool.id.toLowerCase().includes(query) ||
          (tool.shortTitle && tool.shortTitle.toLowerCase().includes(query))
      );
    }

    // 按分类筛选
    if (params.params.category) {
      filteredTools = filteredTools.filter(
        (tool) => tool.category === params.params?.category
      );
    }

    // 按状态筛选
    if (params.params.status) {
      filteredTools = filteredTools.filter(
        (tool) => tool.status === params.params?.status
      );
    }

    // 按日期范围筛选
    if (params.params.dateRange) {
      const [startDate, endDate] = params.params.dateRange;
      filteredTools = filteredTools.filter(
        (tool) => tool.createdAt >= startDate && tool.createdAt <= endDate
      );
    }
  }

  // 计算分页
  const total = filteredTools.length;
  const totalPages = Math.ceil(total / params.pageSize);
  const startIndex = (params.page - 1) * params.pageSize;
  const endIndex = startIndex + params.pageSize;
  const paginatedTools = filteredTools.slice(startIndex, endIndex);

  // 构建分页信息
  const pagination: Pagination = {
    page: params.page,
    pageSize: params.pageSize,
    total,
    totalPages,
    hasNext: params.page < totalPages,
    hasPrev: params.page > 1,
  };

  // 模拟网络延迟
  await new Promise((resolve) => setTimeout(resolve, 300));

  return {
    data: paginatedTools,
    pagination,
  };
};

// 模拟API调用，创建测量工具
const mockCreateTool = async (
  tool: PsychologicalTestSummary
): Promise<PsychologicalTestSummary> => {
  console.log("创建测量工具", tool);
  // 模拟网络延迟
  await new Promise((resolve) => setTimeout(resolve, 300));
  return {
    ...tool,
    id: `tool-${Date.now()}`,
    usersCompleted: 0,
    status: "已启用",
    createdAt: Date.now(),
  };
};

// 模拟API调用，更新测量工具状态
const mockUpdateToolStatus = async (
  id: string,
  status: string
): Promise<boolean> => {
  console.log(`更新测量工具 ${id} 状态为 ${status}`);
  // 模拟网络延迟
  await new Promise((resolve) => setTimeout(resolve, 300));
  return true;
};

// 模拟API调用，删除测量工具
const mockDeleteTool = async (id: string): Promise<boolean> => {
  console.log(`删除测量工具 ${id}`);
  // 模拟网络延迟
  await new Promise((resolve) => setTimeout(resolve, 300));
  return true;
};

/**
 * 测量工具管理Hook
 * 提供测量工具列表的获取、搜索、筛选和状态监听功能
 */
export function useAssessmentTools(
  initialParams: { params?: ToolsListParams } = {}
) {
  const [loading, setLoading] = useState(false);
  const [tools, setTools] = useState<PsychologicalTestSummary[]>([]);
  const [pagination, setPagination] = useState<Pagination>({
    page: 1,
    pageSize: 10,
    total: 0,
    totalPages: 0,
    hasNext: false,
    hasPrev: false,
  });
  const [filters, setFilters] = useState<ToolsListParams>({
    query: "",
    category: undefined,
    status: "",
    dateRange: undefined,
  });
  const [error, setError] = useState<Error | null>(null);

  // 使用ref保存请求参数，避免依赖变化导致函数重建
  const requestParamsRef = useRef({
    filters: initialParams.params,
  });

  // 更新ref值
  useEffect(() => {
    requestParamsRef.current = {
      filters,
    };
  }, [filters]);

  // 统一请求函数
  const fetchTools = useCallback(
    async (params: ToolsListRequest) => {
      if (loading) return;
      setLoading(true);
      setError(null);
      try {
        const res = await mockFetchTools(params);
        setTools(res.data);
        setPagination(res.pagination);
        return res;
      } catch (error) {
        const err =
          error instanceof Error ? error : new Error("获取测量工具失败");
        setError(err);
        console.error("获取测量工具失败:", error);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [loading]
  );

  // 刷新列表
  const refreshTools = useCallback(async () => {
    try {
      await fetchTools({
        page: 1,
        pageSize: pagination?.pageSize || 10,
        params: requestParamsRef.current.filters,
        forceRefresh: true,
      });
    } catch (error) {
      console.error("刷新测量工具失败:", error);
    }
  }, [fetchTools, pagination?.pageSize]);

  // 加载更多/翻页
  const loadMoreTools = useCallback(
    async (page: number) => {
      try {
        await fetchTools({
          page,
          pageSize: pagination?.pageSize || 10,
          params: requestParamsRef.current.filters,
        });
      } catch (error) {
        console.error("加载更多测量工具失败:", error);
      }
    },
    [fetchTools, pagination?.pageSize]
  );

  // 搜索测量工具
  const onSearch = useCallback(
    async (query: string) => {
      const newFilters = {
        ...requestParamsRef.current.filters,
        query,
      };
      setFilters(newFilters);

      try {
        await fetchTools({
          page: 1,
          pageSize: pagination?.pageSize || 10,
          params: newFilters,
        });
      } catch (error) {
        console.error("搜索测量工具失败:", error);
      }
    },
    [fetchTools, pagination?.pageSize]
  );

  // 按分类筛选
  const onCategoryChange = useCallback(
    async (category?: TestCategory) => {
      const newFilters = {
        ...requestParamsRef.current.filters,
        category,
      };
      setFilters(newFilters);

      try {
        await fetchTools({
          page: 1,
          pageSize: pagination?.pageSize || 10,
          params: newFilters,
        });
      } catch (error) {
        console.error("按分类筛选测量工具失败:", error);
      }
    },
    [fetchTools, pagination?.pageSize]
  );

  // 按状态筛选
  const onStatusChange = useCallback(
    async (status: string) => {
      const newFilters = {
        ...requestParamsRef.current.filters,
        status: status || "",
      };
      setFilters(newFilters);

      try {
        await fetchTools({
          page: 1,
          pageSize: pagination?.pageSize || 10,
          params: newFilters,
        });
      } catch (error) {
        console.error("按状态筛选测量工具失败:", error);
      }
    },
    [fetchTools, pagination?.pageSize]
  );

  // 设置日期范围
  const onDateRangeChange = useCallback(
    async (dateRange?: [number, number]) => {
      const newFilters = {
        ...requestParamsRef.current.filters,
        dateRange,
      };
      setFilters(newFilters);

      try {
        await fetchTools({
          page: 1,
          pageSize: pagination?.pageSize || 10,
          params: newFilters,
        });
      } catch (error) {
        console.error("按日期筛选测量工具失败:", error);
      }
    },
    [fetchTools, pagination?.pageSize]
  );

  // 创建测量工具
  const createTool = useCallback(
    async (tool: PsychologicalTestSummary) => {
      setLoading(true);
      setError(null);
      try {
        const newTool = await mockCreateTool(tool);
        // 刷新列表
        await refreshTools();
        return newTool;
      } catch (error) {
        const err =
          error instanceof Error ? error : new Error("创建测量工具失败");
        setError(err);
        console.error("创建测量工具失败:", error);
        throw err;
      } finally {
        setLoading(false);
      }
    },
    [refreshTools]
  );

  // 更新测量工具状态
  const updateToolStatus = useCallback(
    async (id: string, status: string) => {
      setLoading(true);
      setError(null);
      try {
        await mockUpdateToolStatus(id, status);
        // 刷新列表
        await refreshTools();
        return true;
      } catch (error) {
        const err =
          error instanceof Error ? error : new Error("更新测量工具状态失败");
        setError(err);
        console.error("更新测量工具状态失败:", error);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [refreshTools]
  );

  // 删除测量工具
  const deleteTool = useCallback(
    async (id: string) => {
      setLoading(true);
      setError(null);
      try {
        await mockDeleteTool(id);
        // 刷新列表
        await refreshTools();
        return true;
      } catch (error) {
        const err =
          error instanceof Error ? error : new Error("删除测量工具失败");
        setError(err);
        console.error("删除测量工具失败:", error);
        return false;
      } finally {
        setLoading(false);
      }
    },
    [refreshTools]
  );

  // 初始加载
  useEffect(() => {
    refreshTools();
  }, [refreshTools]);

  return {
    tools,
    pagination,
    loading,
    error,
    filters,
    refreshTools,
    loadMoreTools,
    onSearch,
    onCategoryChange,
    onStatusChange,
    onDateRangeChange,
    createTool,
    updateToolStatus,
    deleteTool,
  };
}
