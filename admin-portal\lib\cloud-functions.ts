import {
  CloudFunctionResult,
  OrderListRequest,
  TransactionListRequest,
  WithdrawalListRequest,
} from "@/app/model/api";
import { callCloudFunction } from "./wxcloud";

/**
 * 订单相关云函数
 */
export const orderCloudFunctions = {
  /**
   * 获取订单列表
   */
  getOrders: async (params: OrderListRequest) => {
    return callCloudFunction("admin-portal", "getOrders", params);
  },

  /**
   * 获取退款审核订单列表
   */
  getRefundAuditOrders: async (
    params: OrderListRequest
  ): Promise<CloudFunctionResult> => {
    return callCloudFunction("admin-portal", "getRefundAuditOrders", params);
  },

  approveRefund: async (params: {
    orderId: string;
  }): Promise<CloudFunctionResult> => {
    return callCloudFunction("admin-portal", "approveRefund", params);
  },

  rejectRefund: async (params: {
    orderId: string;
  }): Promise<CloudFunctionResult> => {
    return callCloudFunction("admin-portal", "rejectRefund", params);
  },
};

/**
 * 资金相关云函数
 */
export const financeCloudFunctions = {
  /**
   * 获取交易流水
   */
  getTransactions: async (params?: TransactionListRequest) => {
    return callCloudFunction("admin-portal", "getTransactions", params);
  },

  /**
   * 获取提现申请
   */
  getWithdrawals: async (params?: WithdrawalListRequest) => {
    return callCloudFunction("admin-portal", "getWithdrawals", params);
  },

  /**
   * 处理提现申请
   */
  completeWithdrawal: async (params: {
    id: string;
  }): Promise<CloudFunctionResult> => {
    return callCloudFunction("admin-portal", "completeWithdrawal", params);
  },
};

/**
 * 资源管理相关云函数
 */
export const resourceCloudFunctions = {
  /**
   * 获取测量工具列表
   */
  getAssessmentTools: async (params?: {
    page?: number;
    pageSize?: number;
    status?: string;
    keyword?: string;
  }) => {
    return callCloudFunction("resource", "getAssessmentTools", params);
  },

  /**
   * 创建/更新测量工具
   */
  saveAssessmentTool: async (params: {
    id?: string;
    name: string;
    type: string;
    questions: unknown[];
    status: string;
  }) => {
    return callCloudFunction("resource", "saveAssessmentTool", params);
  },

  /**
   * 获取音乐资源列表
   */
  getMusic: async (params?: {
    page?: number;
    pageSize?: number;
    status?: string;
    category?: string;
    keyword?: string;
  }) => {
    return callCloudFunction("resource", "getMusic", params);
  },

  /**
   * 创建/更新音乐资源
   */
  saveMusic: async (params: {
    id?: string;
    title: string;
    category: string;
    url: string;
    duration: string;
    status: string;
  }) => {
    return callCloudFunction("resource", "saveMusic", params);
  },
};
