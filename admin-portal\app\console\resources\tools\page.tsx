"use client";
import TestForm from "@/app/components/test-form";
import TestJsonEditor from "@/app/components/test-json-editor";
import { useAssessmentTools } from "@/app/hooks/useAssessmentTools";
import { PsychologicalTest } from "@/app/model/test.model";
import { Badge } from "@/components/ui/badge";
import { Button } from "@/components/ui/button";
import { Card } from "@/components/ui/card";
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "@/components/ui/dialog";
import { DotLoading } from "@/components/ui/dot-loading";
import { Input } from "@/components/ui/input";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs";
import { toast } from "@/components/ui/toast";
import { FileEdit, PlusCircle, Trash2 } from "lucide-react";
import { useState } from "react";

const statusStyles = {
  已启用: "bg-green-100 text-green-800",
  未启用: "bg-gray-100 text-gray-800",
};

export default function AssessmentToolsPage() {
  const [open, setOpen] = useState(false);
  const [tab, setTab] = useState("form");
  const [searchInput, setSearchInput] = useState("");

  // 使用自定义hook获取测量工具列表
  const {
    tools,
    pagination,
    loading,
    error,
    refreshTools,
    loadMoreTools,
    onSearch,
    updateToolStatus,
    deleteTool,
    createTool,
  } = useAssessmentTools();

  // 处理搜索
  const handleSearch = () => {
    onSearch(searchInput);
  };

  // 处理搜索框按回车
  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === "Enter") {
      handleSearch();
    }
  };

  // 处理状态变更
  const handleStatusChange = async (id: string, currentStatus: string) => {
    const newStatus = currentStatus === "已启用" ? "未启用" : "已启用";
    const success = await updateToolStatus(id, newStatus);
    if (success) {
      toast.success(`${newStatus === "已启用" ? "启用" : "禁用"}成功`);
    } else {
      toast.error(`${newStatus === "已启用" ? "启用" : "禁用"}失败`);
    }
  };

  // 处理删除
  const handleDelete = async (id: string) => {
    if (window.confirm("确定要删除此测量工具吗？此操作不可恢复。")) {
      const success = await deleteTool(id);
      if (success) {
        toast.success("删除成功");
      } else {
        toast.error("删除失败");
      }
    }
  };

  // 处理表单提交
  const handleSubmit = async (data: PsychologicalTest) => {
    try {
      await createTool(data);
      setOpen(false);
      toast.success("测量工具创建成功");
    } catch (err) {
      toast.error(
        "创建失败：" + (err instanceof Error ? err.message : "未知错误")
      );
    }
  };

  return (
    <div className="space-y-6">
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-3xl font-bold tracking-tight">测量工具管理</h1>
          <p className="text-muted-foreground">管理平台提供的心理测量工具</p>
        </div>
        <Dialog open={open} onOpenChange={setOpen}>
          <DialogTrigger asChild>
            <Button className="flex items-center gap-1">
              <PlusCircle size={18} />
              <span>添加测量工具</span>
            </Button>
          </DialogTrigger>
          <DialogContent className="max-w-4xl">
            <DialogHeader>
              <DialogTitle>新建测量工具</DialogTitle>
            </DialogHeader>
            <Tabs value={tab} onValueChange={setTab} className="w-full">
              <TabsList className="mb-4">
                <TabsTrigger value="form">表单方式</TabsTrigger>
                <TabsTrigger value="json">JSON方式</TabsTrigger>
              </TabsList>
              <TabsContent value="form">
                <TestForm onSubmit={handleSubmit} />
              </TabsContent>
              <TabsContent value="json">
                <TestJsonEditor onSubmit={handleSubmit} />
              </TabsContent>
            </Tabs>
          </DialogContent>
        </Dialog>
      </div>

      <Card className="p-6">
        <div className="flex flex-col md:flex-row gap-4 mb-6">
          <Input
            placeholder="搜索工具名称或ID..."
            className="max-w-sm"
            value={searchInput}
            onChange={(e) => setSearchInput(e.target.value)}
            onKeyPress={handleKeyPress}
          />
          <div className="flex flex-wrap gap-2">
            <Button onClick={handleSearch}>搜索</Button>
            <Button variant="outline" onClick={refreshTools}>
              刷新
            </Button>
          </div>
        </div>

        {loading ? (
          <div className="flex justify-center items-center py-20">
            <DotLoading size={8} color="#1e40af" className="mx-auto" />
          </div>
        ) : error ? (
          <div className="text-center py-8 text-red-500">
            加载失败: {error.message}
          </div>
        ) : (
          <>
            <div className="rounded-md border overflow-hidden">
              <Table>
                <TableHeader>
                  <TableRow>
                    <TableHead>ID</TableHead>
                    <TableHead>工具名称</TableHead>
                    <TableHead>分类</TableHead>
                    <TableHead>题目数量</TableHead>
                    <TableHead>状态</TableHead>
                    <TableHead>操作</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {tools.length > 0 ? (
                    tools.map((tool) => (
                      <TableRow key={tool.id}>
                        <TableCell className="font-medium">{tool.id}</TableCell>
                        <TableCell>
                          <div>
                            <div>{tool.title}</div>
                            {tool.shortTitle && (
                              <div className="text-xs text-muted-foreground">
                                {tool.shortTitle}
                              </div>
                            )}
                          </div>
                        </TableCell>
                        <TableCell>{tool.category}</TableCell>
                        <TableCell>{tool.howmany}</TableCell>
                        <TableCell>
                          <Badge
                            className={
                              statusStyles[
                                tool.status as keyof typeof statusStyles
                              ] || ""
                            }
                          >
                            {(tool.status as string) || "未知"}
                          </Badge>
                        </TableCell>
                        <TableCell>
                          <div className="flex items-center gap-2">
                            <Button
                              variant="outline"
                              size="sm"
                              className="flex items-center gap-1"
                            >
                              <FileEdit size={14} />
                              编辑
                            </Button>
                            <Button
                              variant={
                                tool.status === "已启用" ? "default" : "outline"
                              }
                              size="sm"
                              onClick={() =>
                                handleStatusChange(
                                  tool.id,
                                  tool.status || "未启用"
                                )
                              }
                            >
                              {tool.status === "已启用" ? "禁用" : "启用"}
                            </Button>
                            <Button
                              variant="destructive"
                              size="sm"
                              className="flex items-center gap-1"
                              onClick={() => handleDelete(tool.id)}
                            >
                              <Trash2 size={14} />
                              删除
                            </Button>
                          </div>
                        </TableCell>
                      </TableRow>
                    ))
                  ) : (
                    <TableRow>
                      <TableCell colSpan={6} className="h-24 text-center">
                        暂无测量工具
                      </TableCell>
                    </TableRow>
                  )}
                </TableBody>
              </Table>
            </div>

            {pagination && (
              <div className="flex items-center justify-between mt-4">
                <div className="text-sm text-muted-foreground">
                  共 {pagination.total} 条记录，第 {pagination.page}/
                  {pagination.totalPages} 页
                </div>
                <div className="flex items-center space-x-2">
                  <Button
                    variant="outline"
                    size="sm"
                    disabled={!pagination.hasPrev}
                    onClick={() => loadMoreTools(pagination.page - 1)}
                  >
                    上一页
                  </Button>

                  <Button
                    variant="outline"
                    size="sm"
                    disabled={!pagination.hasNext}
                    onClick={() => loadMoreTools(pagination.page + 1)}
                  >
                    下一页
                  </Button>
                </div>
              </div>
            )}
          </>
        )}
      </Card>
    </div>
  );
}
