import {
  ORDER_STATUS,
  Order_summary,
  REFUND_STATUS,
} from "@/app/model/order.interface";
import { Payment, WithdrawRecord } from "@/app/model/payment.interface";
import {
  therapist_extend,
  therapist_schedule,
  therapist_summary,
  TherapistFilters,
} from "@/app/model/therapist.interface";

export interface Pagination {
  page: number;
  pageSize: number;
  total: number;
  totalPages: number;
  hasNext: boolean;
  hasPrev: boolean;
}

/**
 * 基础响应接口
 * 所有API响应的标准格式
 */
export interface BaseResponse<T = unknown> {
  /** 是否成功 */
  success: boolean;
  /** 响应码 */
  code?: number;
  /** 响应消息 */
  message?: string;
  /** 响应数据 */
  data: T;
}
/**
 * 分页请求参数
 */

export interface PageRequest {
  /** 页码，从1开始 */
  page?: number;
  /** 每页数量，默认20 */
  pageSize?: number;
  /** 排序字段 */
  sortField?: string;
  /** 排序方向: asc-升序, desc-降序 */
  sortOrder?: "asc" | "desc";
}
/**
 * 分页响应结果
 */

export interface PageResponse<T = unknown> {
  /** 是否成功 */
  success: boolean;
  /** 响应码 */
  code?: number;
  /** 响应消息 */
  message?: string;
  /** 数据列表 */
  data: T[];
  /** 分页信息 */
  pagination: {
    /** 当前页码 */
    page: number;
    /** 每页数量 */
    pageSize: number;
    /** 总记录数 */
    total: number;
    /** 总页数 */
    totalPages: number;
    /** 是否有下一页 */
    hasNext: boolean;
    /** 是否有上一页 */
    hasPrev: boolean;
  };
}
/**
 * 咨询师列表请求参数
 */

export interface TherapistListRequest extends PageRequest {
  /** 搜索关键词 */
  keyword?: string;

  /** 排序方式: favorite-按收藏, price-按价格, rating-按评分 默认按照评分*/
  sort?: "favorite" | "price" | "rating";

  /** 筛选条件 */
  filters?: TherapistFilters;

  /** 是否强制刷新，不使用缓存 */
  forceRefresh?: boolean;
}
/**
 * 咨询师列表响应
 */

export type TherapistListResponse = PageResponse<therapist_summary>;
/**
 * 咨询师基本信息响应
 */

export type TherapistSummaryResponse = BaseResponse<therapist_summary>;
/**
 * 咨询师扩展信息响应
 */

export type TherapistExtendInfoResponse = BaseResponse<therapist_extend>;

/**
 * 咨询师排期响应
 */

export type TherapistScheduleResponse = BaseResponse<therapist_schedule[]>;
/**
 * 咨询师评价请求
 */

export interface TherapistReviewRequest {
  /** 咨询师ID */
  therapistId: string;
  /** 页码 */
  page?: number;
  /** 每页数量 */
  pageSize?: number;
  /** 最低评分 */
  minRating?: number;
  /** 只看有内容的评价 */
  hasContent?: boolean;
}

export interface OrderListParams {
  status?: (typeof ORDER_STATUS)[keyof typeof ORDER_STATUS][];
  refundStatus?: (typeof REFUND_STATUS)[keyof typeof REFUND_STATUS][];
  query?: string;
  complaint?: boolean;
  dateRange?: [number, number]; // 添加日期范围字段，Unix 时间戳毫秒数 [开始时间, 结束时间]
}
/**
 * 订单列表请求参数
 */
export interface OrderListRequest {
  page?: number;
  pageSize?: number;
  params?: OrderListParams;
}

/**
 * 订单列表响应
 */
export interface OrderListResponse {
  success: boolean;
  code: number;
  data: Order_summary[];
  pagination?: Pagination;
}

/**
 * 交易记录查询参数
 */
export interface TransactionListParams {
  type?: string; // 交易类型：收入/支出
  status?: string; // 支付状态: pending/paid/refunded/failed
  query?: string; // 搜索关键词
  dateRange?: [number, number]; // 日期范围 [开始时间, 结束时间] Unix时间戳毫秒数
}

/**
 * 交易记录列表请求
 */
export interface TransactionListRequest {
  page?: number;
  pageSize?: number;
  params?: TransactionListParams;
}

/**
 * 交易记录列表响应
 */
export interface TransactionListResponse {
  success: boolean;
  code: number;
  data: Payment[];
  pagination?: Pagination;
  summary?: {
    totalIncome: number;
    totalExpense: number;
    balance: number;
  };
}

/**
 * 提现记录查询参数
 */
export interface WithdrawalListParams {
  status?: string; // 提现状态: pending/processing/completed/failed
  query?: string; // 搜索关键词
  dateRange?: [number, number]; // 日期范围 [开始时间, 结束时间] Unix时间戳毫秒数
}

/**
 * 提现记录列表请求
 */
export interface WithdrawalListRequest {
  page?: number;
  pageSize?: number;
  params?: WithdrawalListParams;
}

/**
 * 提现记录列表响应
 */
export interface WithdrawalListResponse {
  success: boolean;
  code: number;
  data: WithdrawRecord[];
  pagination?: Pagination;
}

/**
 * 云函数返回结果
 */
export const SUCCESS_CODE = 200;
export interface CloudFunctionResult {
  success: boolean;
  code: number;
  message: string;
  [key: string]: unknown;
}
